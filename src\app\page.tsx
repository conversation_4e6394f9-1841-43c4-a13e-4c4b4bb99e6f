import Image from 'next/image'
import Link from 'next/link'

export default function LoginPage() {
  return (
    <div className="grid grid-cols-12 overflow-auto h-screen">
      <div className="relative hidden col-span-12 md:col-span-6 lg:col-span-7 md:block">
        <Image src="/placeholder.svg" alt="placeholder" fill priority />
      </div>
      <div className="col-span-12 md:col-span-6 lg:col-span-5">
        <div className="flex flex-col items-stretch md:p-8 p-6 lg:p-16">
          <div className="flex items-center justify-between relative">
            <Link href="/">
              <Image src="/logo-light.svg" alt="logo" width={103} height={20} />
            </Link>
            <div className="dropdown dropdown-end">
              <div
                tabIndex={0}
                role="button"
                className="btn btn-circle btn-outline border-base-300"
                aria-label="Theme toggle"
              >
                <span className="icon-[solar--sun-2-bold-duotone]"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
